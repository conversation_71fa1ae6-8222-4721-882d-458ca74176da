'use client'

import React, { useState, useEffect } from 'react'
import { 
  PlayIcon, 
  CogIcon, 
  DocumentTextIcon,
  SpeakerWaveIcon,
  ChatBubbleBottomCenterTextIcon,
  PhotoIcon,
  UserGroupIcon,
  FolderIcon,
  EyeIcon,
  PlusIcon,
  XMarkIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import apiService, { 
  Account, 
  VideoMaterial, 
  VideoCategory,
  BackgroundMusic, 
  CoverTemplate,
  TTSVoice
} from '../../services/apiService'
import { ttsVoices, getTTSVoices } from '../../config/ttsConfig'
import { useSettingsStore } from '../../store/settingsStore'
import { useRouter } from 'next/navigation'
import StoryAssignmentTable from '../../components/StoryAssignmentTable'

interface LoadingState {
  accounts: boolean
  materials: boolean
  music: boolean
  templates: boolean
}

export default function BatchGeneratePage() {
  const router = useRouter()
  
  // Data states
  const [accounts, setAccounts] = useState<Account[]>([])
  const [materialCategories, setMaterialCategories] = useState<VideoCategory[]>([])
  const [musicCategories, setMusicCategories] = useState<VideoCategory[]>([])
  const [templates, setTemplates] = useState<CoverTemplate[]>([])

  // Form states
  const [jobName, setJobName] = useState('')
  const [jobDescription, setJobDescription] = useState('')
  
  // Excel upload states
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [stories, setStories] = useState<string[]>([])
  const [titles, setTitles] = useState<string[]>([])
  const [genders, setGenders] = useState<string[]>([])
  const [isUploading, setIsUploading] = useState(false)
  
  // Account selection
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>([])

  // Story assignment
  const [storyAssignments, setStoryAssignments] = useState<{ [key: number]: string }>({})
  
  // Material configuration (只支持随机选择)
  const [selectedMaterialCategory, setSelectedMaterialCategory] = useState<string>('')
  
  // Voice configuration
  const [selectedMaleVoice, setSelectedMaleVoice] = useState<string>('')
  const [selectedFemaleVoice, setSelectedFemaleVoice] = useState<string>('')
  const [speechRate, setSpeechRate] = useState(1.2) // 默认1.2x倍速
  const [dynamicTtsVoices, setDynamicTtsVoices] = useState<TTSVoice[]>([]) // 动态加载的音色列表

  // Settings store
  const { tts, loadFromBackend } = useSettingsStore()

  // Audio settings
  const [speechVolume, setSpeechVolume] = useState(100) // 默认100%
  const [backgroundMusicVolume, setBackgroundMusicVolume] = useState(5) // 默认5%
  const [enableBackgroundMusic, setEnableBackgroundMusic] = useState(true)
  const [maxAudioDuration, setMaxAudioDuration] = useState(20) // 默认20秒

  // Music configuration
  const [musicSelectionMode, setMusicSelectionMode] = useState<'random' | 'specific'>('random')
  const [selectedMusicCategory, setSelectedMusicCategory] = useState<string>('')
  const [selectedMusic, setSelectedMusic] = useState<string>('')
  const [filteredMusic, setFilteredMusic] = useState<BackgroundMusic[]>([])

  // Template configuration
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [coverPosition, setCoverPosition] = useState<'top' | 'center' | 'bottom' | 'custom'>('center')
  const [coverAnimation, setCoverAnimation] = useState('fade_in_out')
  const [customCoverX, setCustomCoverX] = useState('')
  const [customCoverY, setCustomCoverY] = useState('')
  const [animationDuration, setAnimationDuration] = useState(0.5)

  // Subtitle configuration
  const [subtitleFont, setSubtitleFont] = useState('ZY Starry')
  const [availableFonts, setAvailableFonts] = useState<{name: string, family: string, category: string, available: boolean}[]>([])
  const [fontsLoaded, setFontsLoaded] = useState(false)
  const [showAllFonts, setShowAllFonts] = useState(false)
  const [useCustomFont, setUseCustomFont] = useState(false) // 是否使用自定义字体
  const [customFontName, setCustomFontName] = useState('') // 自定义字体名称

  // Transition configuration
  const [enableTransitions, setEnableTransitions] = useState(false)
  const [transitionType, setTransitionType] = useState('fade')
  const [transitionDuration, setTransitionDuration] = useState(0.5)
  const [subtitleSize, setSubtitleSize] = useState(24)
  const [subtitleColor, setSubtitleColor] = useState('#FFFFFF')
  const [subtitlePosition, setSubtitlePosition] = useState<'top' | 'center' | 'bottom'>('center')
  const [wordsPerScreen, setWordsPerScreen] = useState(1) // 每屏单词数，默认1
  const [strokeThickness, setStrokeThickness] = useState(10) // 描边厚度，默认10px
  const [strokeColor, setStrokeColor] = useState('#000000') // 描边颜色，默认黑色
  
  // Video configuration
  const [videoResolution, setVideoResolution] = useState('1080x1920')
  const [videoFps, setVideoFps] = useState(30)
  
  // UI states
  const [loading, setLoading] = useState<LoadingState>({
    accounts: true,
    materials: true,
    music: true,
    templates: true
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load data on component mount
  useEffect(() => {
    loadAllData()
    // 加载设置配置
    loadFromBackend()
  }, [])

  const loadAllData = async () => {
    await Promise.all([
      loadAccounts(),
      loadMaterials(),
      loadMusic(),
      loadTemplates(),
      loadAvailableFonts()
    ])
  }

  // 动态加载音色列表
  useEffect(() => {
    const loadDynamicVoices = async () => {
      if (tts.provider) {
        try {
          const voices = await getTTSVoices(tts.provider)
          setDynamicTtsVoices(voices)

          // 如果当前没有选中音色且有可用音色，自动选择男女音色
          if (voices.length > 0) {
            const maleVoices = voices.filter(v => v.gender === 'male')
            const femaleVoices = voices.filter(v => v.gender === 'female')

            if (!selectedMaleVoice && maleVoices.length > 0) {
              setSelectedMaleVoice(maleVoices[0].id)
            }
            if (!selectedFemaleVoice && femaleVoices.length > 0) {
              setSelectedFemaleVoice(femaleVoices[0].id)
            }
          }
        } catch (error) {
          console.error('加载音色列表失败:', error)
          setDynamicTtsVoices([])
        }
      } else {
        setDynamicTtsVoices([])
      }
    }

    loadDynamicVoices()
  }, [tts.provider, selectedMaleVoice, selectedFemaleVoice])

  // 监听音乐分类变化，加载对应分类的音乐
  useEffect(() => {
    if (selectedMusicCategory && musicSelectionMode === 'specific') {
      loadMusicByCategory(selectedMusicCategory)
    }
  }, [selectedMusicCategory, musicSelectionMode])

  const loadAccounts = async () => {
    try {
      const response = await apiService.accounts.getAccounts()
      if (response.data) {
        setAccounts(response.data)
      }
    } catch (error) {
      console.error('加载账号失败:', error)
    } finally {
      setLoading(prev => ({ ...prev, accounts: false }))
    }
  }

  const loadMaterials = async () => {
    try {
      const categoriesResponse = await apiService.videoMaterials.getCategories()

      if (categoriesResponse.data) {
        // 确保数据包含material_count字段
        const categoriesWithCount = categoriesResponse.data.map((cat: any) => ({
          ...cat,
          material_count: cat.material_count || 0
        }))
        setMaterialCategories(categoriesWithCount)
      }
    } catch (error) {
      console.error('加载视频素材分类失败:', error)
    } finally {
      setLoading(prev => ({ ...prev, materials: false }))
    }
  }

  const loadMusic = async () => {
    try {
      const categoriesResponse = await apiService.backgroundMusic.getCategories()

      if (categoriesResponse.data) {
        // 转换字符串数组为VideoCategory对象数组
        const categoryObjects = categoriesResponse.data.map((name: string, index: number) => ({
          id: `category_${index}`,
          name: name,
          material_count: 0
        }))
        setMusicCategories(categoryObjects)
      }
    } catch (error) {
      console.error('加载背景音乐分类失败:', error)
    } finally {
      setLoading(prev => ({ ...prev, music: false }))
    }
  }

  // 根据分类加载音乐列表
  const loadMusicByCategory = async (category: string) => {
    try {
      const response = await apiService.backgroundMusic.getMusic(category)
      if (response.data) {
        setFilteredMusic(response.data)
      }
    } catch (error) {
      console.error('加载分类音乐失败:', error)
      setFilteredMusic([])
    }
  }

  const loadTemplates = async () => {
    try {
      const response = await apiService.coverTemplates.getTemplates()
      if (response.data) {
        setTemplates(response.data)
      }
    } catch (error) {
      console.error('加载封面模板失败:', error)
    } finally {
      setLoading(prev => ({ ...prev, templates: false }))
    }
  }

  // 字体检测和管理 - 从generate页面复制
  const predefinedFonts = [
    // 专用字体
    { name: 'ZY Starry', family: 'ZY Starry', category: '装饰性' },
    { name: 'ZY Flexible', family: 'FSP DEMO - Flexible Variable', category: '装饰性' },

    // 中文字体 - 添加更多变体名称
    { name: '微软雅黑', family: 'Microsoft YaHei', category: '中文' },
    { name: '微软雅黑 (备用)', family: '微软雅黑', category: '中文' },
    { name: '黑体', family: 'SimHei', category: '中文' },
    { name: '黑体 (备用)', family: '黑体', category: '中文' },
    { name: '宋体', family: 'SimSun', category: '中文' },
    { name: '宋体 (备用)', family: '宋体', category: '中文' },
    { name: '楷体', family: 'KaiTi', category: '中文' },
    { name: '楷体 (备用)', family: '楷体', category: '中文' },
    { name: '仿宋', family: 'FangSong', category: '中文' },
    { name: '仿宋 (备用)', family: '仿宋', category: '中文' },

    // 华文字体系列
    { name: '华文细黑', family: 'STXihei', category: '中文' },
    { name: '华文黑体', family: 'STHeiti', category: '中文' },
    { name: '华文楷体', family: 'STKaiti', category: '中文' },
    { name: '华文宋体', family: 'STSong', category: '中文' },
    { name: '华文仿宋', family: 'STFangsong', category: '中文' },
    { name: '华文中宋', family: 'STZhongsong', category: '中文' },
    { name: '华文琥珀', family: 'STHupo', category: '中文' },
    { name: '华文新魏', family: 'STXinwei', category: '中文' },
    { name: '华文隶书', family: 'STLiti', category: '中文' },
    { name: '华文行楷', family: 'STXingkai', category: '中文' },
    { name: '方正舒体', family: 'FZShuTi', category: '中文' },
    { name: '方正姚体', family: 'FZYaoti', category: '中文' },
    { name: '思源黑体', family: 'Source Han Sans CN', category: '中文' },
    { name: '思源宋体', family: 'Source Han Serif CN', category: '中文' },
    { name: '思源黑体 (备用)', family: 'Noto Sans CJK SC', category: '中文' },
    { name: '思源宋体 (备用)', family: 'Noto Serif CJK SC', category: '中文' },

    // 英文字体
    { name: 'Arial', family: 'Arial', category: '英文' },
    { name: 'Arial Black', family: 'Arial Black', category: '英文' },
    { name: 'Arial Narrow', family: 'Arial Narrow', category: '英文' },
    { name: 'Helvetica', family: 'Helvetica', category: '英文' },
    { name: 'Helvetica Neue', family: 'Helvetica Neue', category: '英文' },
    { name: 'Times New Roman', family: 'Times New Roman', category: '英文' },
    { name: 'Times', family: 'Times', category: '英文' },
    { name: 'Georgia', family: 'Georgia', category: '英文' },
    { name: 'Verdana', family: 'Verdana', category: '英文' },
    { name: 'Trebuchet MS', family: 'Trebuchet MS', category: '英文' },
    { name: 'Comic Sans MS', family: 'Comic Sans MS', category: '英文' },
    { name: 'Impact', family: 'Impact', category: '英文' },
    { name: 'Courier New', family: 'Courier New', category: '英文' },
    { name: 'Courier', family: 'Courier', category: '英文' },
    { name: 'Palatino', family: 'Palatino', category: '英文' },
    { name: 'Book Antiqua', family: 'Book Antiqua', category: '英文' },
    { name: 'Century Gothic', family: 'Century Gothic', category: '英文' },
    { name: 'Lucida Console', family: 'Lucida Console', category: '英文' },
    { name: 'Tahoma', family: 'Tahoma', category: '英文' },
    { name: 'Calibri', family: 'Calibri', category: '英文' },
    { name: 'Cambria', family: 'Cambria', category: '英文' },
    { name: 'Consolas', family: 'Consolas', category: '英文' },

    // 系统字体
    { name: 'System UI', family: 'system-ui', category: '系统' },
    { name: 'Sans Serif', family: 'sans-serif', category: '系统' },
    { name: 'Serif', family: 'serif', category: '系统' },
    { name: 'Monospace', family: 'monospace', category: '系统' },
    { name: 'Cursive', family: 'cursive', category: '系统' },
    { name: 'Fantasy', family: 'fantasy', category: '系统' },
  ]

  const checkFontAvailability = (fontFamily: string): boolean => {
    try {
      // 方法1: 使用document.fonts API (现代浏览器支持)
      if ('fonts' in document) {
        // 检查字体是否在字体集中
        const fontFace = new FontFace('test-font', `local("${fontFamily}")`)
        try {
          // 尝试加载字体
          fontFace.load().then(() => {
            console.log(`字体 ${fontFamily} 可用`)
          }).catch(() => {
            console.log(`字体 ${fontFamily} 不可用`)
          })
        } catch (e) {
          // FontFace构造失败，继续使用Canvas方法
        }
      }

      // 方法2: 改进的Canvas检测方法
      const testStrings = [
        'mmmmmmmmmmlli',      // 原始测试字符串
        '中文测试字体效果',      // 中文字符
        'ABCDEFGHijklmn',     // 英文字符
        '0123456789'          // 数字字符
      ]

      const fontSize = '48px'
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return true // 如果无法创建context，假设字体可用

      canvas.width = 300
      canvas.height = 100

      // 测试多个fallback字体
      const fallbackFonts = ['monospace', 'serif', 'sans-serif']

      for (const testString of testStrings) {
        for (const fallback of fallbackFonts) {
          // 清除画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.textAlign = 'left'
          ctx.fillStyle = 'black'
          ctx.textBaseline = 'top'

          // 绘制使用 fallback 字体的文本
          ctx.font = `${fontSize} ${fallback}`
          ctx.fillText(testString, 10, 10)
          const fallbackData = ctx.getImageData(0, 0, canvas.width, canvas.height)

          // 清除画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)

          // 绘制使用目标字体的文本
          ctx.font = `${fontSize} "${fontFamily}", ${fallback}`
          ctx.fillText(testString, 10, 10)
          const testData = ctx.getImageData(0, 0, canvas.width, canvas.height)

          // 比较图像数据
          let diffCount = 0
          const totalPixels = fallbackData.data.length / 4

          for (let i = 0; i < fallbackData.data.length; i += 4) {
            if (fallbackData.data[i] !== testData.data[i] ||
                fallbackData.data[i + 1] !== testData.data[i + 1] ||
                fallbackData.data[i + 2] !== testData.data[i + 2] ||
                fallbackData.data[i + 3] !== testData.data[i + 3]) {
              diffCount++
            }
          }

          // 如果差异像素超过1%，认为字体不同（即目标字体可用）
          const diffPercentage = diffCount / totalPixels
          if (diffPercentage > 0.01) {
            return true
          }
        }
      }

      return false // 所有测试都相同，字体不可用
    } catch (error) {
      console.warn('字体检测失败:', fontFamily, error)
      return true // 如果检测失败，假设字体可用
    }
  }

  const loadAvailableFonts = async () => {
    try {
      console.log('开始检测字体...')

      const fontsWithAvailability = predefinedFonts.map(font => {
        const available = checkFontAvailability(font.family)
        console.log(`字体检测: ${font.name} (${font.family}) - ${available ? '可用' : '不可用'}`)
        return {
          ...font,
          available
        }
      })

      // 按类别和可用性排序
      fontsWithAvailability.sort((a, b) => {
        // 首先按可用性排序（可用的在前）
        if (a.available && !b.available) return -1
        if (!a.available && b.available) return 1

        // 然后按类别排序
        const categoryOrder = ['装饰性', '中文', '英文', '系统']
        const aCategoryIndex = categoryOrder.indexOf(a.category)
        const bCategoryIndex = categoryOrder.indexOf(b.category)
        if (aCategoryIndex !== bCategoryIndex) {
          return aCategoryIndex - bCategoryIndex
        }

        // 最后按名称排序
        return a.name.localeCompare(b.name)
      })

      setAvailableFonts(fontsWithAvailability)
      setFontsLoaded(true)

      const availableCount = fontsWithAvailability.filter(f => f.available).length
      console.log(`字体检测完成: ${availableCount}/${fontsWithAvailability.length} 个字体可用`)

    } catch (error) {
      console.error('加载字体失败:', error)
      // 如果检测失败，使用默认字体列表（全部标记为可用）
      const fallbackFonts = predefinedFonts.map(font => ({ ...font, available: true }))
      setAvailableFonts(fallbackFonts)
      setFontsLoaded(true)
    }
  }

  // Handle Excel file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploadedFile(file)
    setIsUploading(true)

    try {
      const result = await apiService.videoGeneration.uploadExcel(file)

      if (result.success) {
        setStories(result.stories)
        setTitles(result.titles || [])
        setGenders(result.genders || [])
        alert(result.message || `成功解析 ${result.total_count} 条文案`)
      } else {
        alert(`文件解析失败: ${result.error}`)
        setUploadedFile(null)
        setStories([])
        setTitles([])
        setGenders([])
      }
    } catch (error) {
      alert(`文件上传失败: ${error}`)
      setUploadedFile(null)
      setStories([])
      setTitles([])
      setGenders([])
    } finally {
      setIsUploading(false)
    }
  }

  // Handle account selection
  const handleAccountToggle = (accountId: string | number) => {
    const accountIdStr = String(accountId)
    setSelectedAccounts(prev =>
      prev.includes(accountIdStr)
        ? prev.filter(id => id !== accountIdStr)
        : [...prev, accountIdStr]
    )
  }

  // Validation
  const isFormValid = () => {
    // 检查是否所有故事都已分配账号
    const allStoriesAssigned = stories.length > 0 &&
      stories.every((_, index) => storyAssignments[index])

    return (
      jobName.trim() &&
      stories.length > 0 &&
      selectedAccounts.length > 0 &&
      allStoriesAssigned &&
      selectedMaterialCategory && // 必须选择素材分组
      selectedMaleVoice &&
      selectedFemaleVoice &&
      selectedTemplate &&
      (!enableBackgroundMusic || musicSelectionMode === 'random' || selectedMusic)
    )
  }

  // Submit form
  const handleSubmit = async () => {
    if (!isFormValid()) {
      alert('请完成所有必填配置项')
      return
    }

    setIsSubmitting(true)
    try {
      const jobData = {
        name: jobName,
        description: jobDescription,
        config: {
          material_selection: 'random' as 'random' | 'manual',
          video_material_group: selectedMaterialCategory,
          selected_materials: undefined,
          voice_settings: {
            male_voice: selectedMaleVoice,
            female_voice: selectedFemaleVoice,
            speed: speechRate,
          },
          audio_settings: {
            speech_volume: speechVolume / 100,
            background_music_volume: backgroundMusicVolume / 100,
            enable_background_music: enableBackgroundMusic,
            max_audio_duration: maxAudioDuration,
          },
          music_selection: musicSelectionMode,
          background_music_group: selectedMusicCategory, // 这里已经是分类名称
          music_id: musicSelectionMode === 'specific' ? selectedMusic : undefined,
          cover_template_id: selectedTemplate,
          cover_settings: {
            position: coverPosition,
            animation: coverAnimation,
            custom_x: coverPosition === 'custom' ? customCoverX : undefined,
            custom_y: coverPosition === 'custom' ? customCoverY : undefined,
            animation_duration: animationDuration
          },
          subtitle_config: {
            font_family: subtitleFont,
            font_size: subtitleSize,
            font_color: subtitleColor,
            position: subtitlePosition,
            words_per_screen: wordsPerScreen,
            stroke_thickness: strokeThickness,
            stroke_color: strokeColor,
            enabled: true,
            include_all_words: true
          },
          video_config: {
            resolution: videoResolution,
            fps: videoFps,
            output_format: 'mp4'
          },
          transition_settings: {
            enabled: enableTransitions,
            transition_type: transitionType,
            duration: transitionDuration
          }
        },
        account_ids: Object.values(storyAssignments), // 使用实际分配的账号
        stories: stories,
        titles: titles.length > 0 ? titles : undefined,
        genders: genders.length > 0 ? genders : undefined
      }

      const response = await apiService.videoGeneration.createBatchJob(jobData)

      if (response.data) {
        alert('批量视频生成作业创建成功！')
        router.push('/tasks')
      } else {
        alert(`创建作业失败: ${response.error}`)
      }
    } catch (error) {
      alert(`提交失败: ${error}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Safe data access
  const safeAccounts = Array.isArray(accounts) ? accounts : []
  const safeTemplates = Array.isArray(templates) ? templates : []
  const safeMaterialCategories = Array.isArray(materialCategories) ? materialCategories : []
  const safeMusicCategories = Array.isArray(musicCategories) ? musicCategories : []

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-8 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">批量视频生成</h1>
          <p className="text-gray-600">基于Excel文案列表批量生成视频</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧配置区域 */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* 基础信息 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <CogIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">基础信息</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    作业名称 *
                  </label>
                  <input
                    type="text"
                    value={jobName}
                    onChange={(e) => setJobName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入作业名称"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    作业描述
                  </label>
                  <textarea
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入作业描述（可选）"
                  />
                </div>
              </div>
            </div>

            {/* Excel文案上传 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <DocumentTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">文案上传</h2>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Excel文件 *
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      accept=".xlsx,.xls"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="excel-upload"
                      disabled={isUploading}
                    />
                    <label
                      htmlFor="excel-upload"
                      className={`cursor-pointer ${isUploading ? 'opacity-50' : ''}`}
                    >
                      {isUploading ? (
                        <ArrowPathIcon className="mx-auto h-12 w-12 text-gray-400 animate-spin" />
                      ) : (
                        <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                      )}
                      <div className="mt-4">
                        <p className="text-sm text-gray-600">
                          {uploadedFile ? uploadedFile.name : '点击上传Excel文件'}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          支持.xlsx和.xls格式，文案内容请放在第一列，视频标题请放在第二列（可选），朗读性别请放在第三列（可选，支持：男/女/male/female）
                        </p>
                      </div>
                    </label>
                  </div>
                </div>

                {stories.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      文案预览 ({stories.length} 条)
                    </label>
                    <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3 bg-gray-50">
                      {stories.map((story, index) => (
                        <div key={index} className="text-sm text-gray-700 mb-3 last:mb-0 border-b border-gray-200 pb-2 last:border-b-0">
                          <div className="flex items-start">
                            <span className="font-medium text-gray-500 mr-2">#{index + 1}:</span>
                            <div className="flex-1">
                              <div className="mb-1">
                                <span className="text-xs text-gray-400">文案：</span>
                                <span>{story}</span>
                              </div>
                              {titles[index] && (
                                <div className="mb-1">
                                  <span className="text-xs text-gray-400">标题：</span>
                                  <span className="text-blue-600 font-medium">{titles[index]}</span>
                                </div>
                              )}
                              {genders[index] && (
                                <div>
                                  <span className="text-xs text-gray-400">性别：</span>
                                  <span className={`font-medium ${genders[index] === 'male' ? 'text-blue-600' : 'text-pink-600'}`}>
                                    {genders[index] === 'male' ? '男性' : '女性'}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 账号选择 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <UserGroupIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">账号选择</h2>
              </div>

              {loading.accounts ? (
                <div className="flex items-center justify-center py-8">
                  <ArrowPathIcon className="h-6 w-6 text-blue-500 animate-spin mr-2" />
                  <span className="text-gray-600">加载账号中...</span>
                </div>
              ) : (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择账号 * (选中的账号将用于故事编排)
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                    {safeAccounts.map(account => (
                      <label key={account.id} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedAccounts.includes(String(account.id))}
                          onChange={() => handleAccountToggle(account.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">{account.name}</span>
                      </label>
                    ))}
                  </div>
                  {selectedAccounts.length > 0 && (
                    <p className="text-sm text-blue-600 mt-2">
                      已选择 {selectedAccounts.length} 个账号
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* 故事编排表格 */}
            {stories.length > 0 && (
              <StoryAssignmentTable
                stories={stories}
                titles={titles}
                genders={genders}
                accounts={safeAccounts}
                selectedAccounts={selectedAccounts}
                onAssignmentChange={setStoryAssignments}
              />
            )}

            {/* 视频素材配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <FolderIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">视频素材</h2>
              </div>

              {loading.materials ? (
                <div className="flex items-center justify-center py-8">
                  <ArrowPathIcon className="h-6 w-6 text-blue-500 animate-spin mr-2" />
                  <span className="text-gray-600">加载素材中...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-4">
                      系统将完全随机选择视频素材进行生成
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      素材分组 *
                    </label>
                    <select
                      value={selectedMaterialCategory}
                      onChange={(e) => setSelectedMaterialCategory(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">选择素材分组</option>
                      {safeMaterialCategories.map(category => (
                        <option key={category.id} value={category.name}>
                          {category.name} ({category.material_count}个素材)
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* 语音配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <SpeakerWaveIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">语音配置</h2>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      男性音色 *
                    </label>
                    <select
                      value={selectedMaleVoice}
                      onChange={(e) => setSelectedMaleVoice(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">选择男性音色</option>
                      {(dynamicTtsVoices.length > 0 ? dynamicTtsVoices : ttsVoices)
                        .filter(voice => voice.gender === 'male')
                        .map(voice => (
                          <option key={voice.id} value={voice.id}>
                            {voice.name} ({voice.language})
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      女性音色 *
                    </label>
                    <select
                      value={selectedFemaleVoice}
                      onChange={(e) => setSelectedFemaleVoice(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">选择女性音色</option>
                      {(dynamicTtsVoices.length > 0 ? dynamicTtsVoices : ttsVoices)
                        .filter(voice => voice.gender === 'female')
                        .map(voice => (
                          <option key={voice.id} value={voice.id}>
                            {voice.name} ({voice.language})
                          </option>
                        ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    语音倍速: {speechRate}x
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    value={speechRate}
                    onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0.5x</span>
                    <span>1.0x</span>
                    <span>2.0x</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 音频设置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <ChatBubbleBottomCenterTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">音频设置</h2>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    语音音量: {speechVolume}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={speechVolume}
                    onChange={(e) => setSpeechVolume(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={enableBackgroundMusic}
                      onChange={(e) => setEnableBackgroundMusic(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">启用背景音乐</span>
                  </label>
                </div>

                {enableBackgroundMusic && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      背景音乐音量: {backgroundMusicVolume}%
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={backgroundMusicVolume}
                      onChange={(e) => setBackgroundMusicVolume(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大音频时长: {maxAudioDuration}秒
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="60"
                    step="5"
                    value={maxAudioDuration}
                    onChange={(e) => setMaxAudioDuration(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>10秒</span>
                    <span>35秒</span>
                    <span>60秒</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    超过此时长的音频将自动变速处理，保持音色不变
                  </p>
                </div>
              </div>
            </div>

            {/* 背景音乐配置 */}
            {enableBackgroundMusic && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center mb-4">
                  <SpeakerWaveIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">背景音乐</h2>
                </div>

                {loading.music ? (
                  <div className="flex items-center justify-center py-8">
                    <ArrowPathIcon className="h-6 w-6 text-blue-500 animate-spin mr-2" />
                    <span className="text-gray-600">加载音乐中...</span>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        选择方式
                      </label>
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="musicSelection"
                            value="random"
                            checked={musicSelectionMode === 'random'}
                            onChange={(e) => setMusicSelectionMode(e.target.value as 'random' | 'specific')}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">随机选择</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="musicSelection"
                            value="specific"
                            checked={musicSelectionMode === 'specific'}
                            onChange={(e) => setMusicSelectionMode(e.target.value as 'random' | 'specific')}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">指定音乐</span>
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        音乐分组
                      </label>
                      <select
                        value={selectedMusicCategory}
                        onChange={(e) => setSelectedMusicCategory(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">选择分组</option>
                        {safeMusicCategories.map(category => (
                          <option key={category.id} value={category.name}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {musicSelectionMode === 'specific' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          选择音乐 *
                        </label>
                        <select
                          value={selectedMusic}
                          onChange={(e) => setSelectedMusic(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={!selectedMusicCategory}
                        >
                          <option value="">
                            {!selectedMusicCategory
                              ? '请先选择音乐分组'
                              : filteredMusic.length === 0
                                ? '该分组下无音乐'
                                : '选择音乐'
                            }
                          </option>
                          {filteredMusic.map(music => (
                            <option key={music.id} value={music.id}>
                              {music.name}
                            </option>
                          ))}
                        </select>
                        {selectedMusicCategory && filteredMusic.length === 0 && (
                          <p className="text-sm text-gray-500 mt-1">
                            该分组下暂无音乐文件
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* 封面模板 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <PhotoIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">封面模板</h2>
              </div>

              {loading.templates ? (
                <div className="flex items-center justify-center py-8">
                  <ArrowPathIcon className="h-6 w-6 text-blue-500 animate-spin mr-2" />
                  <span className="text-gray-600">加载模板中...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      选择模板 *
                    </label>
                    <select
                      value={selectedTemplate}
                      onChange={(e) => setSelectedTemplate(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">选择封面模板</option>
                      {safeTemplates.map(template => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        封面位置
                      </label>
                      <select
                        value={coverPosition}
                        onChange={(e) => setCoverPosition(e.target.value as 'top' | 'center' | 'bottom' | 'custom')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="top">画面上方</option>
                        <option value="center">画面居中</option>
                        <option value="bottom">画面下方</option>
                        <option value="custom">自定义位置</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        动画效果
                      </label>
                      <select
                        value={coverAnimation}
                        onChange={(e) => setCoverAnimation(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="none">无动画</option>
                        <option value="fade_in">淡入</option>
                        <option value="fade_out">淡出</option>
                        <option value="fade_in_out">淡入淡出</option>
                        <option value="slide_in_left">从左滑入</option>
                        <option value="slide_in_right">从右滑入</option>
                        <option value="slide_in_top">从上滑入</option>
                        <option value="slide_in_bottom">从下滑入</option>
                        <option value="slide_out_left">向左滑出</option>
                        <option value="slide_out_right">向右滑出</option>
                        <option value="slide_out_top">向上滑出</option>
                        <option value="slide_out_bottom">向下滑出</option>
                        {/* <option value="zoom_in">缩放进入</option>
                        <option value="zoom_out">缩放退出</option>
                        <option value="zoom_in_out">缩放进出</option> */}
                      </select>
                    </div>
                  </div>

                  {coverPosition === 'custom' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          自定义X位置
                        </label>
                        <input
                          type="text"
                          value={customCoverX}
                          onChange={(e) => setCustomCoverX(e.target.value)}
                          placeholder="如：(main_w-overlay_w)/2"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          自定义Y位置
                        </label>
                        <input
                          type="text"
                          value={customCoverY}
                          onChange={(e) => setCustomCoverY(e.target.value)}
                          placeholder="如：(main_h-overlay_h)/4"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                  )}

                  {coverAnimation !== 'none' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        动画时长（秒）
                      </label>
                      <input
                        type="number"
                        value={animationDuration}
                        onChange={(e) => setAnimationDuration(parseFloat(e.target.value) || 0.5)}
                        min="0.1"
                        max="2.0"
                        step="0.1"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 字幕设置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <ChatBubbleBottomCenterTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">字幕设置</h2>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <label className="block text-sm font-medium text-gray-700">
                        字体
                        {fontsLoaded && !useCustomFont && (
                          <span className="text-xs text-gray-500 ml-2">
                            ({availableFonts.filter(f => showAllFonts || f.available).length}个{showAllFonts ? '' : '可用'})
                          </span>
                        )}
                      </label>
                      <div className="flex items-center space-x-2 text-xs">
                        {fontsLoaded && !useCustomFont && (
                          <label className="flex items-center text-gray-600">
                            <input
                              type="checkbox"
                              checked={showAllFonts}
                              onChange={(e) => setShowAllFonts(e.target.checked)}
                              className="mr-1"
                            />
                            显示所有字体
                          </label>
                        )}
                        <label className="flex items-center text-blue-600">
                          <input
                            type="checkbox"
                            checked={useCustomFont}
                            onChange={(e) => {
                              setUseCustomFont(e.target.checked)
                              if (e.target.checked) {
                                setSubtitleFont(customFontName || 'Arial')
                              } else {
                                setSubtitleFont('ZY Starry')
                              }
                            }}
                            className="mr-1"
                          />
                          自定义字体
                        </label>
                      </div>
                    </div>

                    {useCustomFont ? (
                      <input
                        type="text"
                        value={customFontName}
                        onChange={(e) => {
                          setCustomFontName(e.target.value)
                          setSubtitleFont(e.target.value || 'Arial')
                        }}
                        placeholder="输入字体名称，如：Arial, Microsoft YaHei, 微软雅黑"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        style={{ fontFamily: customFontName || 'Arial' }}
                      />
                    ) : (
                      <select
                        value={subtitleFont}
                        onChange={(e) => setSubtitleFont(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        style={{ fontFamily: subtitleFont }}
                      >
                        {!fontsLoaded ? (
                          <option value="">正在检测字体...</option>
                        ) : (
                          ['装饰性', '中文', '英文', '系统'].map(category => {
                            const categoryFonts = availableFonts.filter(font =>
                              font.category === category && (showAllFonts || font.available)
                            )
                            if (categoryFonts.length === 0) return null

                            return (
                              <optgroup key={category} label={`${category}字体`}>
                                {categoryFonts.map(font => (
                                  <option
                                    key={font.family}
                                    value={font.family}
                                    style={{
                                      fontFamily: font.family,
                                      color: font.available ? 'black' : '#999',
                                      fontWeight: font.available ? 'normal' : 'lighter'
                                    }}
                                    disabled={!font.available && !showAllFonts}
                                  >
                                    {font.name}{!font.available ? ' (系统未检测到)' : ''}
                                  </option>
                                ))}
                              </optgroup>
                            )
                          })
                        )}
                      </select>
                    )}

                    {/* 字体预览 */}
                    {subtitleFont && (
                      <div className="mt-2 p-3 bg-gray-50 rounded border text-sm">
                        <div className="text-xs text-gray-500 mb-1">预览效果：</div>
                        <div style={{ fontFamily: subtitleFont, fontSize: '16px' }}>
                          字体预览 Font Preview 123
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      字号
                    </label>
                    <input
                      type="number"
                      value={subtitleSize}
                      onChange={(e) => setSubtitleSize(parseInt(e.target.value))}
                      min="12"
                      max="72"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      颜色
                    </label>
                    <input
                      type="color"
                      value={subtitleColor}
                      onChange={(e) => setSubtitleColor(e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-lg"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      字幕位置
                    </label>
                    <select
                      value={subtitlePosition}
                      onChange={(e) => setSubtitlePosition(e.target.value as 'top' | 'center' | 'bottom')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="top">上方</option>
                      <option value="center">居中</option>
                      <option value="bottom">下方</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      每屏单词数
                    </label>
                    <input
                      type="number"
                      value={wordsPerScreen}
                      onChange={(e) => setWordsPerScreen(parseInt(e.target.value) || 1)}
                      min="1"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      控制每屏显示的单词数量，默认1个单词
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描边厚度
                    </label>
                    <input
                      type="number"
                      value={strokeThickness}
                      onChange={(e) => setStrokeThickness(parseInt(e.target.value) || 0)}
                      min="0"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      字幕描边厚度，0表示无描边
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描边颜色
                    </label>
                    <input
                      type="color"
                      value={strokeColor}
                      onChange={(e) => setStrokeColor(e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-lg"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      字幕描边颜色，增强文字可读性
                    </p>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-sm text-gray-600">
                    <p className="font-medium mb-2">💡 字幕配置说明：</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                      <div>
                        <p>• <strong>每屏单词数</strong>：控制字幕显示密度</p>
                        <p>• <strong>智能断句</strong>：优先按句子结构显示</p>
                        <p>• <strong>标点处理</strong>：单词后连续标点一起显示</p>
                      </div>
                      <div>
                        <p>• <strong>描边效果</strong>：提升文字在复杂背景下的可读性</p>
                        <p>• <strong>字体检测</strong>：自动检测系统可用字体</p>
                        {fontsLoaded && (
                          <p className="text-green-600 font-medium mt-1">
                            ✓ 检测到 {availableFonts.filter(f => f.available).length} 个可用字体
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 视频设置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <CogIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">视频设置</h2>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      分辨率
                    </label>
                    <select
                      value={videoResolution}
                      onChange={(e) => setVideoResolution(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="1080x1920">1080x1920 (竖屏)</option>
                      <option value="1920x1080">1920x1080 (横屏)</option>
                      <option value="720x1280">720x1280 (竖屏)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      帧率
                    </label>
                    <select
                      value={videoFps}
                      onChange={(e) => setVideoFps(parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={24}>24 FPS</option>
                      <option value={30}>30 FPS</option>
                      <option value={60}>60 FPS</option>
                    </select>
                  </div>
                </div>

                {/* 转场效果配置 */}
                <div className="border-t pt-4">
                  <div className="flex items-center mb-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <input
                        type="checkbox"
                        checked={enableTransitions}
                        onChange={(e) => setEnableTransitions(e.target.checked)}
                        className="mr-2"
                      />
                      启用视频素材转场效果
                    </label>
                  </div>

                  {enableTransitions && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          转场类型
                        </label>
                        <select
                          value={transitionType}
                          onChange={(e) => setTransitionType(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="fade">淡入淡出</option>
                          <option value="dissolve">溶解过渡</option>
                          <option value="slide_left">向左滑动</option>
                          <option value="slide_right">向右滑动</option>
                          <option value="slide_up">向上滑动</option>
                          <option value="slide_down">向下滑动</option>
                          <option value="wipe_left">向左擦除</option>
                          <option value="wipe_right">向右擦除</option>
                          <option value="wipe_up">向上擦除</option>
                          <option value="wipe_down">向下擦除</option>
                          <option value="circle_open">圆形展开</option>
                          <option value="circle_close">圆形收缩</option>
                          <option value="radial">径向过渡</option>
                          <option value="smooth_left">平滑左移</option>
                          <option value="smooth_right">平滑右移</option>
                          <option value="smooth_up">平滑上移</option>
                          <option value="smooth_down">平滑下移</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          转场时长（秒）
                        </label>
                        <input
                          type="number"
                          value={transitionDuration}
                          onChange={(e) => setTransitionDuration(parseFloat(e.target.value) || 0.5)}
                          min="0.1"
                          max="2.0"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          建议0.5秒，过长会影响观看体验
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="bg-blue-50 rounded-lg p-3 mt-3">
                    <p className="text-sm text-blue-700">
                      💡 转场效果会在视频素材之间添加平滑过渡，让视频更加流畅自然
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <button
                onClick={handleSubmit}
                disabled={!isFormValid() || isSubmitting}
                className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium ${
                  isFormValid() && !isSubmitting
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    创建作业中...
                  </>
                ) : (
                  <>
                    <PlayIcon className="h-4 w-4 mr-2" />
                    创建批量视频生成作业
                  </>
                )}
              </button>

              {!isFormValid() && (
                <p className="mt-2 text-sm text-red-600 text-center">
                  请完成所有必填配置项
                </p>
              )}
            </div>
          </div>

          {/* 右侧预览区域 */}
          <div className="space-y-6">
            {/* 作业概览 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">作业概览</h3>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">文案数量:</span>
                  <span className="text-sm font-medium text-gray-900">{stories.length}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">选中账号:</span>
                  <span className="text-sm font-medium text-gray-900">{selectedAccounts.length}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">将生成视频:</span>
                  <span className="text-sm font-medium text-blue-600">{stories.length}</span>
                </div>

                {selectedAccounts.length > 0 && stories.length > 0 && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      文案将按顺序轮流分配给选中的账号生成视频
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* 配置摘要 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">配置摘要</h3>

              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-gray-600">男性音色:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedMaleVoice ? (dynamicTtsVoices.length > 0 ? dynamicTtsVoices : ttsVoices).find(v => v.id === selectedMaleVoice)?.name : '未选择'}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">女性音色:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedFemaleVoice ? (dynamicTtsVoices.length > 0 ? dynamicTtsVoices : ttsVoices).find(v => v.id === selectedFemaleVoice)?.name : '未选择'}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">素材选择:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedMaterialCategory
                      ? `${selectedMaterialCategory} (随机选择)`
                      : '未选择分组'
                    }
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">语音倍速:</span>
                  <span className="ml-2 text-gray-900">{speechRate}x</span>
                </div>

                <div>
                  <span className="text-gray-600">音量设置:</span>
                  <span className="ml-2 text-gray-900">语音{speechVolume}% / 背景音乐{backgroundMusicVolume}%</span>
                </div>

                <div>
                  <span className="text-gray-600">背景音乐:</span>
                  <span className="ml-2 text-gray-900">
                    {enableBackgroundMusic ? (musicSelectionMode === 'random' ? '随机选择' : '指定音乐') : '禁用'}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">字幕字体:</span>
                  <span className="ml-2 text-gray-900" style={{ fontFamily: subtitleFont }}>
                    {availableFonts.find(f => f.family === subtitleFont)?.name || subtitleFont}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">字幕配置:</span>
                  <span className="ml-2 text-gray-900">
                    每屏{wordsPerScreen}词 / {subtitleSize}px / {subtitlePosition === 'top' ? '上方' : subtitlePosition === 'center' ? '居中' : '下方'}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">字幕描边:</span>
                  <span className="ml-2 text-gray-900">
                    {strokeThickness > 0 ? `${strokeThickness}px厚度` : '无描边'}
                    {strokeThickness > 0 && (
                      <span
                        className="inline-block w-4 h-4 rounded ml-1 border border-gray-300"
                        style={{ backgroundColor: strokeColor }}
                      ></span>
                    )}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">封面模板:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedTemplate ? safeTemplates.find(t => t.id === selectedTemplate)?.name : '未选择'}
                  </span>
                </div>

                <div>
                  <span className="text-gray-600">视频分辨率:</span>
                  <span className="ml-2 text-gray-900">{videoResolution}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
