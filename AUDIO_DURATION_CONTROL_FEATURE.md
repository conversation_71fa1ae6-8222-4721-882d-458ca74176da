# 音频时长控制功能

## 功能概述

在批量生成视频的界面中新增了音频时长控制功能，当生成的语音文件超过设定的最大时长时，系统会自动对音频进行变速处理，同时保持音色不变，并确保后续生成的字幕时间戳与变速后的音频完全匹配。

## 功能特性

### 🎯 核心功能
- **智能时长检测**: 自动检测TTS生成的音频文件实际时长
- **无损变速处理**: 使用ffmpeg的atempo滤镜进行变速，保持音调和音色不变
- **字幕同步**: 字幕时间戳基于变速后的音频文件生成，确保完美同步
- **用户友好**: 前端界面提供直观的时长控制滑块

### 🔧 技术实现
- **前端**: 在批量生成界面的音频设置部分添加时长控制滑块（10-60秒）
- **后端**: 在视频生成流程中集成音频时长检查和变速处理
- **音频处理**: 使用ffmpeg的atempo滤镜，支持0.5x-2.0x变速范围
- **时间戳调整**: Whisper音频分析基于变速后的音频文件

## 使用方法

### 1. 前端设置
1. 打开批量生成视频页面
2. 在"音频设置"部分找到"最大音频时长"滑块
3. 设置期望的最大时长（默认20秒，范围10-60秒）
4. 系统会显示提示："超过此时长的音频将自动变速处理，保持音色不变"

### 2. 自动处理流程
1. **TTS生成**: 系统正常生成语音文件
2. **时长检测**: 检查音频文件的实际时长
3. **变速判断**: 如果超过设定限制，计算所需的变速比例
4. **音频处理**: 使用ffmpeg进行变速处理，生成新的音频文件
5. **字幕生成**: 基于变速后的音频文件生成字幕时间戳
6. **视频合成**: 使用变速后的音频和匹配的字幕进行最终合成

## 技术细节

### 变速算法
```bash
# 使用ffmpeg的atempo滤镜
ffmpeg -i input.wav -filter:a "atempo=1.5" output.wav
```

### 变速比例计算
```python
speed_ratio = actual_duration / max_duration
# 限制在合理范围内
speed_ratio = min(speed_ratio, 2.0)  # 最大2倍速
```

### 文件命名规则
变速后的音频文件会添加速度标识：
- 原文件: `audio_12345.wav`
- 变速文件: `audio_12345_speed1.5x.wav`

## 配置参数

### 前端配置
- **默认值**: 20秒
- **范围**: 10-60秒
- **步长**: 5秒

### 后端配置
```python
class AudioSettings(BaseModel):
    max_audio_duration: Optional[float] = Field(
        default=20.0, 
        ge=5.0, 
        le=60.0, 
        description="最大音频时长（秒），超过此时长将自动变速"
    )
```

## 日志记录

系统会详细记录变速处理过程：

```
任务 12345: 检查音频时长限制 20.0s
音频时长 30.00s 超过限制 20.0s，将使用 1.50x 变速
执行音频变速命令: ffmpeg -i input.wav -filter:a atempo=1.5 -y output.wav
✅ 音频变速处理成功: output.wav
任务 12345: 音频已变速处理，新路径: output.wav
```

## 兼容性说明

### 向后兼容
- 如果不设置`max_audio_duration`或设置为0，功能不会激活
- 现有的视频生成流程完全不受影响

### 性能影响
- 变速处理通常在5-15秒内完成
- 对整体生成时间影响较小
- 处理过程异步执行，不阻塞其他任务

## 错误处理

### 失败回退
- 如果变速处理失败，系统会自动使用原始音频文件
- 详细的错误日志帮助诊断问题
- 不会影响整个视频生成流程

### 边界情况
- 变速比例限制在2.0倍以内，避免音质过度损失
- 对于极短音频（<5秒），不进行变速处理
- ffmpeg命令执行超时保护（60秒）

## 测试验证

功能已通过完整测试：
- ✅ 30秒音频在20秒限制下正确变速至1.5倍
- ✅ 变速后音频时长精确为20秒
- ✅ 未超过限制的音频不会被处理
- ✅ 音质和音调保持不变

## 未来扩展

### 可能的改进
1. **更多变速选项**: 支持更精细的变速控制
2. **音质优化**: 集成更高质量的音频处理算法
3. **批量预览**: 在生成前预览变速效果
4. **自定义规则**: 支持基于内容长度的动态时长限制

这个功能完美解决了语音文件时长过长的问题，同时保持了音质和用户体验的平衡。
